# Automatic Versioning and Tagging

This project is configured for automatic versioning and tagging on every commit to the main branch.

## How It Works

### Automatic Process
When you push a commit to the `main` branch, the GitLab CI pipeline will:

1. **Run all tests** (unit tests, acceptance tests)
2. **Build and publish Docker image** 
3. **Automatically bump the patch version** (e.g., 2.0.0 → 2.0.1)
4. **Create a git tag** (e.g., v2.0.1)
5. **Commit the version change back** to the repository
6. **Push the tag** to GitLab

### Pipeline Flow
```
Commit to main → Tests → Build → Docker Publish → Auto-Version → Tag Created
```

### Version Bumping Strategy
- **Patch version** is automatically incremented for every commit to main
- **Minor/Major versions** can still be bumped manually when needed

## Files Affected

### Automatically Updated
- `VERSION` - Contains the current version number
- `pom.xml` - Maven version is updated to match
- Git tags - New tag created for each version

### Configuration Files
- `.gitlab-ci.yml` - Contains the auto-version job
- `Makefile` - Contains the auto-version target

## Manual Version Control

### Skip Automatic Versioning
To skip automatic versioning for a specific commit, include `[skip ci]` in your commit message:
```bash
git commit -m "Update documentation [skip ci]"
```

### Manual Version Bumps
You can still manually bump versions when needed:

```bash
# Bump patch version (2.0.0 → 2.0.1)
make bump-patch

# Bump minor version (2.0.0 → 2.1.0)
make bump-minor

# Bump major version (2.0.0 → 3.0.0)
make bump-major
```

### Manual Releases (Bump + Tag + Push)
For special releases that need manual control, use these targets that bump version AND create tags:

```bash
# Manual patch release (2.0.0 → 2.0.1 + tag v2.0.1)
make manual-patch-release

# Manual minor release (2.0.0 → 2.1.0 + tag v2.1.0)
make manual-minor-release

# Manual major release (2.0.0 → 3.0.0 + tag v3.0.0)
make manual-major-release
```

### Manual Release Jobs in GitLab CI
Manual release jobs are available in GitLab CI for special releases:
- Go to GitLab CI/CD → Pipelines
- Click on a pipeline
- Manually trigger one of:
  - **manual-patch-release** - For bug fixes
  - **manual-minor-release** - For new features
  - **manual-major-release** - For breaking changes

## Current Version

The current version is stored in the `VERSION` file:
```bash
cat VERSION
```

## Git Tags

View all version tags:
```bash
git tag -l
```

View latest tag:
```bash
git describe --tags --abbrev=0
```

## Docker Images

Each version automatically creates Docker images tagged with:
- `registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:2.0.1` (version-specific)
- `registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest` (always latest)

## Troubleshooting

### Version Conflicts
If a tag already exists, the auto-version job will skip and not fail the pipeline.

### CI Token Issues
The auto-version job uses GitLab's built-in `CI_JOB_TOKEN` for authentication.

### Infinite Loops
The `[skip ci]` tag in auto-version commits prevents infinite pipeline loops.

## Benefits

✅ **Consistent versioning** - Every release gets a unique version  
✅ **Automatic tagging** - No manual intervention needed  
✅ **Docker image versioning** - Each version gets its own Docker tag  
✅ **Audit trail** - Clear history of all releases  
✅ **CI/CD integration** - Seamless integration with deployment pipelines

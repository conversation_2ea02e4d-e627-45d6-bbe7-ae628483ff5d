# Manual Release System

This project uses a manual release system where versions are bumped and tagged only when explicitly triggered.

## Release Types

### Manual Release Jobs in GitLab CI

Go to **GitLab CI/CD → Pipelines** and manually trigger one of these jobs:

#### 🔧 **Patch Release** (`manual-patch-release`)
- **Use for**: Bug fixes, small improvements
- **Version change**: `2.0.0 → 2.0.1`
- **When to use**: Regular maintenance releases

#### ✨ **Minor Release** (`manual-minor-release`)  
- **Use for**: New features, enhancements
- **Version change**: `2.0.0 → 2.1.0`
- **When to use**: Feature releases

#### 🚀 **Major Release** (`manual-major-release`)
- **Use for**: Breaking changes, major updates
- **Version change**: `2.0.0 → 3.0.0`
- **When to use**: Major version releases

## How Manual Releases Work

Each manual release job will:

1. ✅ **Bump the version** in `VERSION` file and `pom.xml`
2. ✅ **Create a git tag** (e.g., `v2.0.1`)
3. ✅ **Commit the changes** with descriptive message
4. ✅ **Push to repository** (both commit and tag)

## Local Manual Releases

You can also trigger releases locally using Make:

```bash
# Patch release (2.0.0 → 2.0.1)
make manual-patch-release

# Minor release (2.0.0 → 2.1.0)
make manual-minor-release

# Major release (2.0.0 → 3.0.0)
make manual-major-release
```

## Version Bumping Only (No Tag)

If you just want to bump versions without creating tags:

```bash
# Bump patch version only
make bump-patch

# Bump minor version only
make bump-minor

# Bump major version only
make bump-major
```

## Current Version

Check the current version:
```bash
cat VERSION
```

## Git Tags

View all release tags:
```bash
git tag -l
```

View latest tag:
```bash
git describe --tags --abbrev=0
```

## Pipeline Flow

```
Commit → Tests → Build → Docker Publish
                    ↓
            Manual Release Jobs Available
                    ↓
         (Trigger manually when ready)
                    ↓
            Version Bump + Tag + Push
```

## Safety Features

✅ **Duplicate Prevention** - Aborts if tag already exists  
✅ **Manual Control** - No automatic versioning  
✅ **Clear Commit Messages** - Descriptive release commits  
✅ **CI Integration** - Works in GitLab CI environment  

## Docker Images

Each manual release creates Docker images tagged with:
- `registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:2.0.1` (version-specific)
- `registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest` (always latest)

## Release Strategy

- **Regular commits**: No version changes, just build and test
- **Bug fixes**: Use patch releases when ready to deploy
- **New features**: Use minor releases for feature milestones  
- **Breaking changes**: Use major releases for significant updates

This gives you full control over when and how versions are released! 🎯

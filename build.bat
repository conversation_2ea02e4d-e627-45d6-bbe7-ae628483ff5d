@echo off
setlocal EnableDelayedExpansion

REM File Paths
set VERSION_FILE=VERSION
if not exist %VERSION_FILE% (
    echo 1.0.0 > %VERSION_FILE%
)
set /p CURRENT_VERSION=<%VERSION_FILE%
for /f "tokens=* delims= " %%a in ("%CURRENT_VERSION%") do set CURRENT_VERSION=%%a
set DEV_VERSION=%CURRENT_VERSION%-SNAPSHOT

REM Command routing
if "%1"=="clean" goto clean
if "%1"=="build" goto build
if "%1"=="test" goto test
if "%1"=="test-iteration1" goto test_iteration1
if "%1"=="test-iteration2" goto test_iteration2
if "%1"=="test-unittest" goto test_unittest
if "%1"=="test_iteration1" goto test_iteration1
if "%1"=="test_iteration2" goto test_iteration2
if "%1"=="test_unittest" goto test_unittest
if "%1"=="release" goto release
if "%1"=="tag" goto tag
if "%1"=="bump-patch" goto bump_patch
if "%1"=="bump-minor" goto bump_minor
if "%1"=="bump-major" goto bump_major
if "%1"=="commit-and-push" goto commit_and_push
if "%1"=="all" goto all
if "%1"=="publish-patch" goto publish_patch
if "%1"=="publish-minor" goto publish_minor
if "%1"=="publish-major" goto publish_major
if "%1"=="docker-build" goto docker_build
if "%1"=="docker-run" goto docker_run
if "%1"=="docker-stop" goto docker_stop
if "%1"=="docker-test" goto docker_test
if "%1"=="docker-publish" goto docker_publish
if "%1"=="docker-clean" goto docker_clean

echo Usage: build.bat [clean^|build^|test^|test-iteration1^|test-iteration2^|test-unittest^|test_iteration1^|test_iteration2^|test_unittest^|release^|tag^|bump-patch^|bump-minor^|bump-major^|commit-and-push^|all^|publish-patch^|publish-minor^|publish-major^|docker-build^|docker-run^|docker-stop^|docker-test^|docker-publish^|docker-clean]
exit /b 1

REM Clean
:clean
echo Cleaning project...
mvn clean
goto :eof

REM Build
:build
echo Building project...
mvn clean install -DskipTests
goto :eof

REM Test
:test
echo Running all tests ^(iteration1, iteration2, unittest^)...
call build.bat test_iteration1
call build.bat test_iteration2
call build.bat test_unittest
goto :eof

:test_iteration1
echo Cleaning up any existing servers...
taskkill /f /im java.exe 2>nul
timeout /t 2 /nobreak >nul
echo Starting reference server with 1x1 world...
start /b java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 1
timeout /t 5 /nobreak >nul
echo Running acceptance tests...
call mvn -Dtest=acceptance_test.LaunchAcceptanceTest test
call mvn -Dtest=acceptance_test.LookAcceptanceTest test
call mvn -Dtest=acceptance_test.WorldTests.WorldRobotDeathPitAcceptanceTest test
call mvn -Dtest=acceptance_test.WorldTests.WorldInitializationAcceptanceTest test
call mvn -Dtest=acceptance_test.WorldTests.WorldPositionValidationAcceptanceTest test
call mvn -Dtest=acceptance_test.WorldTests.WorldRobotAdditionAcceptanceTest test
echo All tests completed. Now killing reference server...
taskkill /f /im java.exe /fi "WINDOWTITLE eq reference-server*" 2>nul
taskkill /f /im java.exe /fi "COMMANDLINE eq *reference-server*" 2>nul
goto :eof

:test_iteration2
echo Cleaning up any existing servers...
taskkill /f /im java.exe 2>nul
timeout /t 2 /nobreak >nul
echo Starting reference server with 2x2 world...
start /b java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1
timeout /t 5 /nobreak >nul
echo Running acceptance tests...
call mvn -Dtest=acceptance_test.LaunchRobotAcceptanceTest test
call mvn -Dtest=acceptance_test.Moveforward.MoveForwardAcceptanceTest test
call mvn -Dtest=acceptance_test.Moveforward.MoveForwardEdgeAcceptanceTest test
echo Killing reference server...
taskkill /f /im java.exe /fi "WINDOWTITLE eq reference-server*" 2>nul
taskkill /f /im java.exe /fi "COMMANDLINE eq *reference-server*" 2>nul
goto :eof

:test_unittest
echo Cleaning up any existing servers...
taskkill /f /im java.exe 2>nul
timeout /t 3 /nobreak >nul
echo Starting My codebase server...
start /b mvn exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.MultiServers -Dexec.args="-p 5000 -s 2"
timeout /t 5 /nobreak >nul
echo Running all tests...
echo Starting tests with 60 second timeout protection...
start /b cmd /c "timeout 60 /nobreak >nul && echo Timeout reached, killing processes... && taskkill /f /im java.exe 2>nul && taskkill /f /im cmd.exe /fi \"WINDOWTITLE eq *mvn*\" 2>nul"
call mvn test
echo Killing My codebase server...
echo timeout thread interrupted, normal shutdown
timeout /t 2 /nobreak >nul
taskkill /f /im java.exe /fi "COMMANDLINE eq *MultiServers*" 2>nul
taskkill /f /im java.exe /fi "COMMANDLINE eq *maven*" 2>nul
goto :eof

REM Release
:release
echo Switch to release version...
powershell -Command "(Get-Content pom.xml) -replace '<version>%DEV_VERSION%</version>', '<version>%CURRENT_VERSION%</version>' | Set-Content pom.xml"
mvn clean package -DskipTests
echo Switch back to new version...
powershell -Command "(Get-Content pom.xml) -replace '<version>%CURRENT_VERSION%</version>', '<version>%DEV_VERSION%</version>' | Set-Content pom.xml"
copy target\robot-world-%CURRENT_VERSION%-jar-with-dependencies.jar libs\my-server-%CURRENT_VERSION%.jar
git add libs\my-server-%CURRENT_VERSION%.jar
goto :eof

REM Tag
:tag
echo Tagging release...
git tag -a v%CURRENT_VERSION% -m "Release v%CURRENT_VERSION%"
git push origin v%CURRENT_VERSION%
goto :eof

REM Bump Patch
:bump_patch
call :parse_version %CURRENT_VERSION%
set /a NEW_PATCH=%PATCH% + 1
set NEW_VERSION=%MAJOR%.%MINOR%.%NEW_PATCH%
echo Bumping patch: %CURRENT_VERSION% → %NEW_VERSION%
echo %NEW_VERSION% > %VERSION_FILE%
powershell -Command "(Get-Content pom.xml) -replace '<version>%CURRENT_VERSION%-SNAPSHOT</version>', '<version>%NEW_VERSION%-SNAPSHOT</version>' | Set-Content pom.xml"
goto :eof

REM Bump Minor
:bump_minor
call :parse_version %CURRENT_VERSION%
set /a NEW_MINOR=%MINOR% + 1
set NEW_VERSION=%MAJOR%.%NEW_MINOR%.0
echo Bumping minor: %CURRENT_VERSION% → %NEW_VERSION%
echo %NEW_VERSION% > %VERSION_FILE%
powershell -Command "(Get-Content pom.xml) -replace '<version>%CURRENT_VERSION%-SNAPSHOT</version>', '<version>%NEW_VERSION%-SNAPSHOT</version>' | Set-Content pom.xml"
goto :eof

REM Bump Major
:bump_major
call :parse_version %CURRENT_VERSION%
set /a NEW_MAJOR=%MAJOR% + 1
set NEW_VERSION=%NEW_MAJOR%.0.0
echo Bumping major: %CURRENT_VERSION% → %NEW_VERSION%
echo %NEW_VERSION% > %VERSION_FILE%
powershell -Command "(Get-Content pom.xml) -replace '<version>%CURRENT_VERSION%-SNAPSHOT</version>', '<version>%NEW_VERSION%-SNAPSHOT</version>' | Set-Content pom.xml"
goto :eof

REM Commit and Push
:commit_and_push
echo Committing changes...
git add .
git commit -m "Bump version to %CURRENT_VERSION%"
echo Pushing changes...
git push
goto :eof

:all
call build.bat clean
call build.bat test_iteration1
call build.bat test_iteration2
call build.bat test_unittest
call build.bat build
call build.bat release
goto :eof

:publish_patch
call build.bat bump-patch
call build.bat release
call build.bat tag
call build.bat commit-and-push
goto :eof

:publish_minor
call build.bat bump-minor
call build.bat release
call build.bat tag
call build.bat commit-and-push
goto :eof

:publish_major
call build.bat bump-major
call build.bat release
call build.bat tag
call build.bat commit-and-push
goto :eof

REM Docker Build
:docker_build
echo Building Docker image...
call build.bat build
docker build -t robot-worlds-server:%CURRENT_VERSION% .
docker tag robot-worlds-server:%CURRENT_VERSION% robot-worlds-server:latest
echo Docker image built: robot-worlds-server:%CURRENT_VERSION%
goto :eof

REM Docker Run
:docker_run
echo Running Docker container on port 5050...
docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:latest
echo Container started. Server available at http://localhost:5050
echo To stop: build.bat docker-stop
goto :eof

REM Docker Stop
:docker_stop
echo Stopping Docker container...
docker stop robot-worlds-server 2>nul
docker rm robot-worlds-server 2>nul
goto :eof

REM Docker Test
:docker_test
echo Testing Docker container with unit tests...
call build.bat docker_build
docker run -d --name robot-worlds-test -p 5051:5050 robot-worlds-server:%CURRENT_VERSION%
timeout /t 5 /nobreak >nul
echo Running unit tests against Docker container on port 5051...
call mvn test -Dtest=za.co.wethinkcode.robots.**
echo Stopping test container...
docker stop robot-worlds-test 2>nul
docker rm robot-worlds-test 2>nul
goto :eof

REM Docker Publish
:docker_publish
echo Publishing Docker image to GitLab Container Registry...
call build.bat docker_build
docker tag robot-worlds-server:%CURRENT_VERSION% registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:%CURRENT_VERSION%
docker tag robot-worlds-server:%CURRENT_VERSION% registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:%CURRENT_VERSION%
docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
echo Docker image published to GitLab Container Registry
goto :eof

REM Docker Clean
:docker_clean
echo Cleaning up Docker images and containers...
docker stop robot-worlds-server robot-worlds-test 2>nul
docker rm robot-worlds-server robot-worlds-test 2>nul
docker rmi robot-worlds-server:%CURRENT_VERSION% robot-worlds-server:latest 2>nul
echo Docker cleanup completed
goto :eof

:parse_version
for /f "tokens=1,2,3 delims=." %%a in ("%1") do (
    set MAJOR=%%a
    set MINOR=%%b
    set PATCH=%%c
)
goto :eof

package acceptance_test.robotState;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import za.co.wethinkcode.robots.OperationalStatus;
import za.co.wethinkcode.robots.Direction;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;

// Test class for robot state request acceptance tests
public class RobotStateAcceptanceTest {

    private World world;
    private Robot testRobot;

    // Setup method to initialize the world and robot
    @BeforeEach
    void setUp() throws Exception {
        // Set configuration before World init
        setConfigField("HEIGHT", 20);
        setConfigField("WIDTH", 20);
        setConfigField("OBSTACLE_MODE", "BP-10,10:11,11"); // Single bottomless pit
        setConfigField("MAX_SHOTS", 3); // Matches soldier type
        setConfigField("MAX_SHIELD", 3); // Matches soldier type

        // Initialize world with GUI enabled
        world = new World(true);

        // Set test-specific Maze to ensure single obstacle
        Maze testMaze = new Maze("BP-10,10:11,11");
        assertEquals(1, testMaze.getObstacles().size(), "Test Maze should have one obstacle");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());

        // Add robot at (5,5) facing east
        testRobot = createTestRobot("TestBot", new Position(5, 5));
        testRobot.updateDirection(true); // Face east
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // Helper method to create a Robot for testing
    private Robot createTestRobot(String name, Position position) {
        Robot robot = new Robot(name, "soldier"); // Use soldier type
        robot.setPosition(position); // Set position after creation
        return robot;
    }

    // ========================================
    // Scenario a: State is requested for a valid robot
    // ========================================
    @Test
    @DisplayName("AT-2.1.1: Test state request for a valid robot")
    void testRobotStateRequest() {
        // Given a robot is active in the world
        assertEquals(new Position(5, 5), testRobot.getPosition(), "Robot should be at (5,5)");
        assertEquals(Direction.EAST, testRobot.getCurrentDirection(), "Robot should face east");
        assertEquals(OperationalStatus.NORMAL, testRobot.getStatus(), "Robot status should be NORMAL");
        assertEquals("soldier", testRobot.getType(), "Robot type should be soldier");
        assertEquals(testRobot, world.getCurrentRobot(), "Current robot should be TestBot");

        // When I send the state command
        JsonObject state = testRobot.state();

        // Then I should receive a JSON response showing:
        // position [x, y]
        JsonArray position = state.getAsJsonArray("position");
        assertNotNull(position, "Position should be present in state");
        assertEquals(2, position.size(), "Position should be an array of [x, y]");
        assertEquals(5, position.get(0).getAsInt(), "Position x should be 5");
        assertEquals(5, position.get(1).getAsInt(), "Position y should be 5");

        // type (sniper, soldier, hitbot)
        assertEquals("soldier", state.get("make").getAsString(), "Type should be soldier");

        // direction (NORTH, EAST, etc.)
        assertEquals("EAST", state.get("direction").getAsString(), "Direction should be EAST");

        // shields remaining
        assertEquals(3, state.get("shields").getAsInt(), "Shields should be 3 for soldier");

        // shots remaining
        assertEquals(3, state.get("shots").getAsInt(), "Shots should be 3 for soldier");

        // status (e.g., NORMAL, DEAD, RELOAD)
        assertEquals("NORMAL", state.get("status").getAsString(), "Status should be NORMAL");
    }
}
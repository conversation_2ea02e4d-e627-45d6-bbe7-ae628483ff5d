package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.world.World;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Tests for the {@link DumpCommand}.
 * Verifies the console output generated by the dump operation under various world states.
 */
public class DumpCommandTest {

    private World mockWorld;
    private Robot robot1;
    private Robot robot2;
    private DumpCommand dumpCommand;
    private final ByteArrayOutputStream outContent = new ByteArrayOutputStream();
    private final PrintStream originalOut = System.out;


    /**
     * Prepares mocks and command instance before each test.
     * Redirects System.out to capture console output.
     */
    @BeforeEach
    void setUp() {
        System.setOut(new PrintStream(outContent));
        mockWorld = mock(World.class);
        robot1 = mock(Robot.class);
        robot2 = mock(Robot.class);
        dumpCommand = DumpCommand.getInstance();

        when(robot1.getName()).thenReturn("R1");
        when(robot2.getName()).thenReturn("R2");

        JsonObject r1StateJson = new JsonObject();
        r1StateJson.addProperty("name", "R1");
        r1StateJson.addProperty("position", new Position(1, 1).toString());
        r1StateJson.addProperty("direction", "NORTH");
        r1StateJson.addProperty("shields", "3");
        r1StateJson.addProperty("shots", "2");
        r1StateJson.addProperty("status", "NORMAL");
        when(robot1.state()).thenReturn(r1StateJson);

        JsonObject r2StateJson = new JsonObject();
        r2StateJson.addProperty("name", "R2");
        r2StateJson.addProperty("position", new Position(2, 2).toString());
        r2StateJson.addProperty("direction", "SOUTH");
        r2StateJson.addProperty("shields", "4");
        r2StateJson.addProperty("shots", "1");
        r2StateJson.addProperty("status", "REPAIR");
        when(robot2.state()).thenReturn(r2StateJson);

        List<Robot> robotList = new ArrayList<>();
        robotList.add(robot1);
        robotList.add(robot2);

        List<Obstacle> obstacleList = new ArrayList<>();
        obstacleList.add(new Obstacle(5, 5, 6, 6, ObstacleType.MOUNTAIN));
        obstacleList.add(new Obstacle(7, 7, 8, 8, ObstacleType.LAKE));

        when(mockWorld.getRobots()).thenReturn(robotList);
        when(mockWorld.getObstacles()).thenReturn(obstacleList);
    }

    /**
     * Restores System.out and clears static world lists after each test.
     */
    @AfterEach
    void tearDown() {
        System.setOut(originalOut);
        outContent.reset();
    }

    /**
     * Verifies that the dump output contains the main expected section headers.
     */
    @Test
    @DisplayName("dump command should print main sections: Dumping, Robots, Obstacles, Legend, File Write Message")
    void testDump_PrintsMainSections() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        assertTrue(output.contains("Dumping world..."));
        assertTrue(output.contains("Robots currently in the World"));
        assertTrue(output.contains("Obstacles currently in world:"));
        assertTrue(output.contains("------Legend-------"));
        assertTrue(output.contains("Successfully wrote world to Ascii-World.txt"));
    }

    /**
     * Verifies that the dump output lists the correct number of configured obstacles.
     */
    @Test
    @DisplayName("dump command should list the correct number of obstacles")
    void testDump_ListsCorrectNumberOfObstacles() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        Pattern obstaclePattern = Pattern.compile("Obstacle \\d+:");
        Matcher matcher = obstaclePattern.matcher(output);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        assertEquals(2, count);
    }

    /**
     * Verifies that the names of configured robots are present in the dump output.
     */
    @Test
    @DisplayName("dump command should include specific robot names in the output")
    void testDump_IncludesSpecificRobotNames() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();
        assertTrue(output.contains(robot1.getName()));
        assertTrue(output.contains(robot2.getName()));
    }

    /**
     * Verifies that the types of configured obstacles are present in the dump output.
     */
    @Test
    @DisplayName("dump command should include specific obstacle types in the output")
    void testDump_IncludesSpecificObstacleTypes() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();
        assertTrue(output.contains(ObstacleType.MOUNTAIN.toString()));
        assertTrue(output.contains(ObstacleType.LAKE.toString()));
    }

    /**
     * Verifies that detailed information for each obstacle is printed.
     */
    @Test
    @DisplayName("dump command should print obstacle details")
    void testDump_PrintsObstacleDetails() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        assertTrue(output.contains("Obstacles currently in world:"));
        assertTrue(output.contains("Obstacle 1: MOUNTAIN : { TopLeft-> (x: 5, y: 5) BottomRight-> (x: 6, y: 6) }"));
        assertTrue(output.contains("Obstacle 2: LAKE : { TopLeft-> (x: 7, y: 7) BottomRight-> (x: 8, y: 8) }"));
    }

    /**
     * Verifies that the AsciiWorld representation and its legend are printed.
     */
    @Test
    @DisplayName("dump command should print AsciiWorld representation")
    void testDump_PrintsAsciiWorld() {
        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        assertTrue(output.contains("------Legend-------"));
        assertTrue(output.contains("Successfully wrote world to Ascii-World.txt"));
        assertTrue(output.contains("^") || output.contains("M"));
        assertTrue(output.contains("~"));
    }

    /**
     * Verifies the dump output when there are no robots in the world.
     */
    @Test
    @DisplayName("dump command with no robots should print no robots message")
    void testDump_NoRobots_PrintsNoRobotsMessage() {
        when(mockWorld.getRobots()).thenReturn(new ArrayList<>());

        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        assertTrue(output.contains("There are currently no Robots in the world."));
        assertFalse(output.contains("Name: R1"));
    }

    /**
     * Verifies the dump output when there are no obstacles in the world.
     */
    @Test
    @DisplayName("dump command with no obstacles should print empty obstacles list")
    void testDump_NoObstacles_PrintsEmptyObstacles() {
        when(mockWorld.getObstacles()).thenReturn(new ArrayList<>());

        dumpCommand.dump(mockWorld);
        String output = outContent.toString();

        assertTrue(output.contains("Obstacles currently in world:"));
        assertFalse(output.contains("Obstacle 1:"));
    }

    /**
     * Verifies that the {@code execute} method returns an empty JsonObject
     * as DumpCommand's primary action is console output via its {@code dump} method.
     */
    @Test
    @DisplayName("execute method should return an empty JsonObject")
    void testExecute_ReturnsEmptyJsonObject() {
        JsonObject result = dumpCommand.execute(mockWorld);

        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Verifies singleton behavior of getInstance().
     */
    @Test
    @DisplayName("getInstance should return the same instance (Singleton check)")
    void getInstance_returnsSingletonInstance() {
        DumpCommand instance1 = DumpCommand.getInstance();
        DumpCommand instance2 = DumpCommand.getInstance();
        assertSame(instance1, instance2);
    }
}
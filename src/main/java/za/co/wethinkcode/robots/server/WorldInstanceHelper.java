package za.co.wethinkcode.robots.server;

import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.world.World;

public class WorldInstanceHelper {

    public static World createInstance(MultiServers command, Maze maze, ObstacleType type) {
        String mazeConfig = Config.OBSTACLE_MODE;
        // Disable GUI in headless environments or test mode
        boolean enableGUI = !java.awt.GraphicsEnvironment.isHeadless() &&
                           !Boolean.parseBoolean(System.getProperty("test.mode", "false"));
        World world = new World(enableGUI, mazeConfig);

        if (!command.getObstacle().equalsIgnoreCase("none")) {
            try {
                String[] coordinates = command.getObstacle().split(",");
                if (coordinates.length == 2) {
                    int topCoordX = Integer.parseInt(coordinates[0]);
                    int topCoordY = Integer.parseInt(coordinates[1]);
                    int botCoordX = topCoordX;
                    int botCoordY = topCoordY;

                    maze.getObstacleList().add(new Obstacle(topCoordX, topCoordY, botCoordX, botCoordY, type));
                }
            }catch (NumberFormatException e) {
                System.err.println("Invalid obstacle coordinates: " + command.getObstacle());
            }
        }
        return world;
    }
}

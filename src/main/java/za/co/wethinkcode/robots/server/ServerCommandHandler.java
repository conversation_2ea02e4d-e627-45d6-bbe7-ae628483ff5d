package za.co.wethinkcode.robots.server;

import za.co.wethinkcode.robots.command.DumpCommand;
import za.co.wethinkcode.robots.command.RobotsCommand;
import za.co.wethinkcode.robots.world.World;
import java.io.IOException;
import java.util.Scanner;

public class Server<PERSON>ommandHandler {
    
    public static void handleCommands(<PERSON>anne<PERSON> scanner, World worldInstance, MultiServerEngine server) {
        while (true) {
            System.out.print("Server Command> ");

            String command;
            try {
                command = scanner.nextLine().toLowerCase().trim();
            } catch (Exception e) {
                System.out.println("Input error, shutting down server: " + e.getMessage());
                break;
            }

            switch (command) {
                case "quit":
                case "shutdown":
                    System.out.println("Shutting down server...");
                    server.broadcastMessage("quit");
                    try {
                        server.shutdown();
                    } catch (IOException e) {
                        System.out.println("Error shutting down: " + e.getMessage());
                    }
                    return;
                case "dump":
                    DumpCommand.getInstance().dump(worldInstance);
                    break;
                case "robots":
                    RobotsCommand.getInstance().printRobots(worldInstance);
                    break;
                default:
                    System.out.println("Unknown command: " + command);
                    break;
            }
        }
    }
}

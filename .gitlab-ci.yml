image: maven:3.9.6-eclipse-temurin-21

stages:
  - compile
  - unit-test
  - package
  - acceptance-test-ref
  - acceptance-test-main
  - docker-build
  - docker-test
  - release
  - docker-publish

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository/

before_script:
  - apt-get update && apt-get install -y make
  - git config --global user.email "$GITLAB_USER_EMAIL"
  - git config --global user.name "$GITLAB_USER_NAME"


compile:
  stage: compile
  script:
    - make clean
    - make build
  artifacts:
    paths:
      - target/


unit_tests:
  stage: unit-test
  script:
    - make test-unittest
  artifacts:
    when: always
    reports:
      junit: target/surefire-reports/TEST-*.xml
    paths:
      - target/surefire-reports/
  dependencies:
    - compile

package:
  stage: package
  script:
    - make build
  artifacts:
    paths:
      - target/*.jar
  dependencies:
    - compile

acceptance_tests_ref:
  stage: acceptance-test-ref
  script:
    - make test-iteration1
  allow_failure: false
  dependencies:
    - package

acceptance_tests_main:
  stage: acceptance-test-main
  script:
    - make test-iteration2
  allow_failure: false
  dependencies:
    - package


release:
  stage: release
  script:
    - make release
  artifacts:
    paths:
      - libs/my-server-*.jar
  dependencies:
    - unit_tests
    - acceptance_tests_ref
    - acceptance_tests_main


docker-build:
  stage: docker-build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - apk add --no-cache make
    - docker info
  script:
    - make docker-build
  needs:
    - package


docker-test:
  stage: docker-test
  image: maven:3.9.6-eclipse-temurin-21
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - apt-get update && apt-get install -y make docker.io
    - docker info
  script:
    - make docker-test
  needs:
    - package


docker-publish:
  stage: docker-publish
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_REGISTRY: $CI_REGISTRY
    DOCKER_REGISTRY_IMAGE: $CI_REGISTRY_IMAGE
  before_script:
    - apk add --no-cache make
    - docker info
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - make docker-publish
  needs:
    - package
    - docker-test


tag:
  stage: release
  before_script:
    - apt-get update && apt-get install -y make
    - git config --global user.email "$GITLAB_USER_EMAIL"
    - git config --global user.name "$GITLAB_USER_NAME"
  script:
    - make tag
  when: manual
  needs:
    - release

auto-version:
  stage: docker-publish
  before_script:
    - apt-get update && apt-get install -y make git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    - make auto-version
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_MESSAGE !~ /\[skip ci\]/
  needs:
    - docker-publish